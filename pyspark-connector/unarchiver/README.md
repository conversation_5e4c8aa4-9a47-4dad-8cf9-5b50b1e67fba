Unarchiver to decompress raw source files.

# Install Anaconda (Python 3 version)
# https://conda.io/docs/user-guide/install/macos.html

# Setup env
cd connector/pyspark-connector/unarchiver

conda create --name unarchiver python=3.8.12 -c conda-forge

# Activate env
conda activate unarchiver

# install requirements
python3 -m pip install -r requirements_dev.txt

# install system dependencies
brew install p7zip

# Deactivate env
conda deactivate

# Build
python setup.py sdist bdist_wheel
ls dist/

# Run tests (note the pyspark requires a proper Java setup)
pytest
# or with detailed log
pytest --verbose --show-capture=all -vv

# Install package
pip install -e .
# and for dev/testing with extra dependencies
pip install -e .[dev]

# Integration tests
python
import logging
import sys
logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logging.getLogger("py4j").setLevel(logging.WARNING)  # Disable py4j INFO logs
from unarchiver.unarchiver import main
main('lilly.aetion.com/upload/flatiron_testing/201906/', 'lilly.aetion.com/etl/flatiron_testing/201906/raw-temp/', '')

# Runtime configuration

## spark.aetion.unarchiver.upload_block_size

We are using multipart uploads to S3, which means that the files are uploaded in parts.
The default block size is 256 MB.  However, a file cannot be split into more than 10,000 parts,
which means that the maximum file size is 2.45 TB. If you want to upload larger files, you need to increase the block size.

The spark configuration parameter `spark.aetion.unarchiver.upload_block_size` (unit: megabytes) can be used to set the block size. The value is in megabytes. Examples:

  - `512`: 512 MB
  - `256m`: 256 MB
  - `1g`: 1 GB
