[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "unarchiver"
version = "0.0.4"
description = "Unarchiver to decompress raw source files"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "boto3==1.36.3",
    "botocore==1.36.3",
    "s3fs==2025.2.0",
    "py7zr==1.0.0",
    "libarchive-c==5.3",
    "smart-open==7.2.0",
]

[project.optional-dependencies]

dev = [
    "pyspark==3.5.4",
    "pytest",
    "flake8",
    "black",
    "mypy",
]

ci = [
    "setuptools",
    "wheel",
    "pkginfo",
    "importlib_metadata<5",  # https://github.com/python/importlib_metadata/issues/406
]


[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["unarchiver*"]

[tool.setuptools.package-data]
"unarchiver" = ["logging/*.ini"]


[tool.setuptools.exclude-package-data]
"*" = ["__pycache__"]