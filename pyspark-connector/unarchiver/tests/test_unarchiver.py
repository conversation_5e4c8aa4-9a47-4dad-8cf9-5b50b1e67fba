from unittest.mock import Mock
from tests.conftest import MockFS

import logging
import sys

from unarchiver.unarchiver import Unarchiver

logging.basicConfig(stream=sys.stdout, level=logging.INFO)


def test_copy(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/raw1/",
        MockFS.NULL_DEVICE,
        "",
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/raw1/file11.txt", "file11.txt"),
        ("tests/resources/upload_data/raw1/file12.txt", "file12.txt"),
    }


def test_7z(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/7z/archive/",
        MockFS.NULL_DEVICE,
        "Password1",
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/7z/archive/raw1.7z", "_all_"),
        ("tests/resources/upload_data/7z/archive/raw2.7z", "_all_"),
    }


def test_gz(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/gz/archive/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(warns) == {
        (
            "tests/resources/upload_data/gz/archive/file14.txt.unknown",
            None,
            "Unsupported archive: tests/resources/upload_data/gz/archive/file14.txt.unknown. skipping.",
        )
    }
    assert set(entries_success) == {
        ("tests/resources/upload_data/gz/archive/file11.txt.gz", "file11.txt"),
        ("tests/resources/upload_data/gz/archive/file12.txt.gz", "file12.txt"),
        ("tests/resources/upload_data/gz/archive/file13.txt", "file13.txt"),
        ("tests/resources/upload_data/gz/archive/subdir/file15.txt", "file15.txt"),
        ("tests/resources/upload_data/gz/archive/subdir/file16.txt.gz", "file16.txt"),
    }


def test_zip(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/zip/archive/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/zip/archive/raw1.zip", "file11.txt"),
        ("tests/resources/upload_data/zip/archive/raw1.zip", "file12.txt"),
        ("tests/resources/upload_data/zip/archive/raw2.zip", "file21.txt"),
    }


def test_zip_password(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/zip/password/",
        MockFS.NULL_DEVICE,
        "iforget",
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/zip/password/password.zip", "test.txt")
    }


def test_zip_password_from_data_secret(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/zip/password/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/zip/password/password.zip", "test.txt")
    }


def test_zip_password_wrong(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/zip/password/",
        MockFS.NULL_DEVICE,
        "wrong_password",
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 1
    assert len(entries_success) == 0


def test_raw_whitespaces(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/raw3_whitespaces/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        (
            "tests/resources/upload_data/raw3_whitespaces/file 31.txt",
            "file_31.txt",
        )
    }


def test_zip_whitespaces(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/zip/whitespaces/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/zip/whitespaces/raw3.zip", "file_31.txt")
    }


def test_tar(sc, mock_fs_factory):
    unarchiver = Unarchiver(
        "tests/resources/upload_data/tar/gz/",
        MockFS.NULL_DEVICE,
        None,
        mock_fs_factory,
    )
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    assert len(errors) == 0
    assert len(warns) == 0
    assert set(entries_success) == {
        ("tests/resources/upload_data/tar/gz/file.tar.gz", "_all_"),
    }


def test_7z_non_dist(sc, mock_fs_factory):
    from unarchiver.algo.decompressor_7z_non_dist import Decompressor7zNotDist

    upload_file_mock = Mock()
    mock_fs_factory.upload_file = upload_file_mock
    decompressor = Decompressor7zNotDist(mock_fs_factory)
    result = decompressor.decompress_entry(
        "tests/resources/upload_data/7z/archive/raw1.7z",
        "_all_",
        "Password1",
        "s3://bucket/output/",
    )
    assert result == "_all_"
    assert upload_file_mock.call_count == 2
    expected_target_s3_urls = [
        "s3://bucket/output/file11.txt",
        "s3://bucket/output/file12.txt",
    ]
    actual_target_s3_urls = [call.args[1] for call in upload_file_mock.call_args_list]
    assert set(actual_target_s3_urls) == set(expected_target_s3_urls)
    actual_local_files = [call.args[0] for call in upload_file_mock.call_args_list]
    assert any(p.endswith("/uncompressed/file11.txt") for p in actual_local_files)
    assert any(p.endswith("/uncompressed/file12.txt") for p in actual_local_files)
