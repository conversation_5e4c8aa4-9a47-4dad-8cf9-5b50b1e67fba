from io import IOBase
import os
import pytest
import sys
from pyspark.sql import SparkSession
from shutil import copyfile


@pytest.fixture(scope="function")
def sc():
    os.environ["PYSPARK_PYTHON"] = sys.executable
    os.environ["PYSPARK_DRIVER_PYTHON"] = sys.executable

    spark = (
        SparkSession.builder.master("local[2]")
        .appName("my - local - testing - pyspark - context")
        .getOrCreate()
    )
    spark.sparkContext.setLogLevel("WARN")
    return spark.sparkContext


class MockFS:
    NULL_DEVICE = "/dev/null"

    read_timeout = 15
    default_block_size = 5 * 2**20

    def walk(self, path, detail=None):
        if detail:
            raise NotImplementedError
        return os.walk(path)

    def find(self, path, detail=None):
        if detail:
            raise NotImplementedError
        out = []
        for current, dirs, files in self.walk(path):
            out.extend([os.path.join(current, file) for file in files])
        return sorted(out)

    def info(self, path):
        return {"Key": path, "size": 1024}

    def upload_stream(self, f_in: IOBase, target_key: str, **kargs):
        if MockFS.NULL_DEVICE in target_key:
            target_key = MockFS.NULL_DEVICE

        with open(target_key, "wb") as f_out:
            while True:
                data = f_in.read(MockFS.default_block_size)
                if not data:
                    break
                f_out.write(data)

    @staticmethod
    def exists(path):
        return os.path.exists(path)

    def open(self, file, mode, **kwargs):
        if MockFS.NULL_DEVICE in file:
            file = MockFS.NULL_DEVICE
        return open(file, mode)

    @staticmethod
    def copy_basic(path1, path2):
        pass

    @staticmethod
    def get(path, filename, **kwargs):
        copyfile(path, filename)

    @staticmethod
    def put(filename, path, **kwargs):
        pass

    @staticmethod
    def download_file(path, filename, **kwargs):
        copyfile(path, filename)

    @staticmethod
    def upload_file(filename, path, **kwargs):
        pass


@pytest.fixture(scope="function")
def mock_fs_factory():
    return MockFS
