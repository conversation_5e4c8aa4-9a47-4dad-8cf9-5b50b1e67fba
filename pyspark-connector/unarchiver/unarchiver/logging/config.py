"""
Logging configuration utilities for Aetion's PySpark applications.

This module provides functions and classes to configure logging settings from
environment variables, configuration files, or default settings. It supports
resolving logging configurations dynamically based on package structure and
falling back to basic logging if needed.
"""

from functools import partial
import importlib.resources
import logging.handlers
import os
from pathlib import Path
from typing import Optional, Union
import logging.config as logging_config


AETION_LOGGING_CONFIG_FILE_ENV_VARNAME = "AETION_LOGGING_CONFIG_FILE"
DEFAULT_PACKAGE = __package__

logger = logging.getLogger(__name__)

LOG_LEVEL_ENV_VARNAMES = [
    "AETION_LOGGING_LEVEL",
    "LOGLEVEL",
    "LOG_LEVEL",
    "PYTHON_LOGLEVEL",
]

ACCEPTABLE_LOG_LEVELS = [
    "CRITICAL",
    "FATAL",
    "ERROR",
    "WARN",
    "WARNING",
    "INFO",
    "DEBUG",
    "NOTSET",
]


def _get_config_file_from_package() -> Optional[Path]:
    if importlib.resources.is_resource(__package__, "logging.ini"):
        with importlib.resources.path(__package__, "logging.ini") as path:
            return path
    else:
        logger.error(
            f"Logging file not found in package {__package__}. Check the package structure."
        )
    return None


def _get_config_file_from_env() -> Optional[Path]:
    """
    Retrieves the logging configuration file from the default package.

    Returns:
        Optional[Path]: The path to the logging configuration file if found.
    """
    maybe_logging_file: Optional[str] = os.getenv(
        AETION_LOGGING_CONFIG_FILE_ENV_VARNAME, None
    )
    logging_file = Path(maybe_logging_file) if maybe_logging_file else None
    if not logging_file:
        return None
    elif logging_file.exists():
        return logging_file
    else:
        logger.error(
            f"Logging file {logging_file} does not exist. Check the environment variable {AETION_LOGGING_CONFIG_FILE_ENV_VARNAME}"  # noqa
        )
        return None


def _get_config_file_from_file(file: Union[str, Path, None]) -> Optional[Path]:
    if file is None:
        return None
    path = Path(file)
    if path.exists():
        return path
    return None


def setup(logging_file: Union[None, str, Path] = None):
    """
    Configures the logging system for Aetion's Python applications.

    This function sets up logging using a specified logging configuration file,
    if provided, or resolves a logging file dynamically. If no valid logging
    configuration is found, it falls back to a basic logging setup.

    Args:
        logging_file (Union[None, str, Path], optional):
            The path to a logging configuration file. If None, the function
            attempts to resolve the file using environment variables,
            the calling module's package, or a default package.

    Returns:
        Optional[Path]: The resolved logging file path if found and successfully loaded.

    Behavior:
    - Initializes a basic logging setup first to ensure logs are captured even if
      a more advanced configuration fails.
    - Resolves a logging configuration file from:
        1. Environment variable `AETION_LOGGING_CONFIG_FILE`
        2. The package of the calling module
        3. A default package (`__package__`)
    - If a valid logging configuration file is found, it loads the settings.
    - If an error occurs, logs an error message and defaults to basic logging.

    Example:
        setup("custom_logging.ini")  # Loads logging settings from the specified file.
        setup()  # Attempts to resolve a logging config file or falls back to basic logging.
    """
    try:
        setup_basic()
        config_file_path = (
            _get_config_file_from_file(logging_file)
            or _get_config_file_from_env()  # noqa
            or _get_config_file_from_package()  # noqa
        )
        if config_file_path and config_file_path.exists():
            _setup_from_file(config_file_path)
            return config_file_path
    except Exception as e:
        logging.basicConfig(
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            level="INFO",
            force=True,
        )
        err_msg = (
            f"Error setting up logging configuration. Using the basic settings| {e}"
        )
        logging.getLogger(__name__).error(err_msg)


def _setup_from_file(logging_file: Path):
    logging_config.fileConfig(logging_file, disable_existing_loggers=False)
    log_level = _get_log_level()
    if log_level:
        _set_log_level(log_level)
    logger.info(f"Logging configuration loaded from {logging_file}.")


def setup_basic():
    log_level = _get_log_level() or "INFO"
    errmsg = None
    if log_level not in ACCEPTABLE_LOG_LEVELS:
        errmsg = f"Invalid log level: {log_level}. Using INFO instead. Check env variables [{', '.join(LOG_LEVEL_ENV_VARNAMES)}]"  # noqa
        log_level = "INFO"

    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        level=log_level,
        force=True,
    )
    if errmsg:
        logger.warning(errmsg)
    logger.info(f"Initialized basic logging configuration ({log_level=}).")


def _get_log_level() -> Optional[str]:
    getenv_or_none = partial(os.getenv, default=None)
    log_level = next(filter(None, map(getenv_or_none, LOG_LEVEL_ENV_VARNAMES)), None)
    return log_level.strip().upper() if log_level else None


def _set_log_level(log_level: str):
    """
    Sets the logging level for the root logger and all existing handlers.

    Args:
        log_level (str): The logging level to be set (e.g., "INFO", "DEBUG", "ERROR").

    Behavior:
    - Sets the log level for the root logger.
    - Ensures that all future loggers inherit this log level.
    - Updates the log level of all existing handlers.

    Example:
        set_log_level("DEBUG")  # Sets the log level to DEBUG for all loggers and handlers.
    """

    # Ensures all future loggers inherit this level
    logging.root.setLevel(log_level)

    # ensures that even if log messages pass through different handlers
    # (e.g., console output, file logging), they respect the new logging level.
    # This is necessary because the root logger's level is not propagated to handlers.
    for handler in logging.getLogger().handlers:
        handler.setLevel(log_level)


if __name__ == "__main__":
    setup()
    logger = logging.getLogger(__name__)
    logger.info("Logging system configured.")
