import logging
import tempfile
from os import path

import py7zr

from unarchiver.algo.decompressor_base import DecompressorBase

logger = logging.getLogger(__name__)

WAIT_CYCLES = 60
WAIT_BETWEEN_CYCLES_SEC = 60


class Decompressor7z(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return []
        # return ['.7z', '.7zip']

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key, password):
        fs = self.fs_factory()

        with fs.open(source_key, "rb") as f_in:
            with py7zr.SevenZipFile(f_in, mode="r", password=password) as archive:
                entries = [
                    (file_info.filename, file_info.uncompressed)
                    for file_info in archive.list()
                    if not file_info.is_directory
                ]

                logger.info(
                    "a list of (%d) entries (7z) for %s\n%s",
                    len(entries),
                    source_key,
                    entries,
                )

                return entries

    def decompress_entry(
        self, source_key: str, entry_name: str, password: str, target_path: str
    ):

        normalized_entry = self.normalize_entry_name(entry_name)
        target_key = path.join(target_path, normalized_entry)

        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            with py7zr.SevenZipFile(
                source_key, mode="r", password=password
            ) as compressed_archiver:
                logger.info(
                    "[START] decompress (7z) entry %s:%s", source_key, entry_name
                )
                compressed_archiver.extract(targets=[entry_name], path=tmp_file.name)
                logger.info(
                    "[COMPLETED] decompress (7z) entry %s:%s\r%s",
                    source_key,
                    entry_name,
                    tmp_file.name,
                )

        fs = self.fs_factory()
        logger.info(
            "upload (7z) entry %s:%s to %s.", source_key, entry_name, target_key
        )
        fs.upload_file(tmp_file.name, target_key)

        return normalized_entry
