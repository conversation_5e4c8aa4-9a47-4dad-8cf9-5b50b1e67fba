from contextlib import contextmanager
import logging
import os

import tempfile
from os import path
from pathlib import Path
import uuid
import py7zr
import shutil

from unarchiver.algo.decompressor_base import DecompressorBase
from unarchiver.extended_s3fs import ExtendedS3FileSystem

logger = logging.getLogger(__name__)

ALL_ENTRIES = "_all_"

try :
    import libarchive
except ImportError:
    libarchive = None


class Decompressor7zNotDist(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".7z", ".7zip"]

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key, password):
        return [(ALL_ENTRIES, 0)]




    def decompress_entry(
        self, source_key: str, entry_name: str, password: str, target_path: str
    ):
        """
        Decompress all entries of a 7z archive to a target s3 path.

        Args:
            source_key (str): The path/key to the source 7z archive file.
                Example: "ucb.aetion.com/upload/cprd/20250731/Clinical.7z"
            entry (str): The entry identifier (must be "_all_" for this decompressor).
                Example: "_all_"
            password (str): Password for the encrypted archive, or None/empty string for unencrypted.
                Example: "mypassword123" or ""
            target_path (str): The base path where decompressed files will be uploaded.
                Example: "ucb.aetion.com/etl/cprd/20250731/raw/"

        Returns:
            str: The entry identifier that was processed (always "_all_").
        """
        assert entry_name == ALL_ENTRIES
        logging.info(
            "decompressing %s:%s and uploading to %s",
            source_key,
            entry_name,
            target_path,
        )

        if libarchive:
            self._decompress_all_using_streaming(source_key, password, target_path)
        else:
            self._decompress_all_using_local_filesystem(source_key, password, target_path)
        pass



    def _decompress_all_using_streaming(
        self, source_key: str, password: str| None, target_path: str
    ):
        """
        Decompress all entries of a 7z archive to a target s3 path using streaming from the source s3 and the target s3 files.

        Args:
            source_key (str): the S3 URL to the source 7z archive file. Example: s3://ucb.aetion.com/upload/cprd/20250731/Clinical.7z
            password (str | None): The password for the 7z archive. Example: "mypassword123"
            target_path (str): The S3 URL to the base path where decompressed files will be uploaded. Example: s3://ucb.aetion.com/etl/cprd/20250731/raw/
        """

        fs: ExtendedS3FileSystem = self.fs_factory()
        download_block_size = 8 * 1024 * 1024
        with fs.open(source_key, block_size=download_block_size, cache_type="none") as f_in:
            with libarchive.seekable_stream_reader(f_in, passphrase=password) as archive:

           with libarchive.file_reader(f_in, passphrase=password) as entries:
                for entry in entries:
                    if entry.is_dir():
                        continue
                    target_key = path.join(target_path, entry.pathname)
                    logger.info("upload (7z) entry %s to %s.", entry.pathname, target_key)
                    with sopen(target_key, "wb") as f_out:
                        shutil.copyfileobj(entry, f_out)



    def _decompress_all_using_local_filesystem(
        self, source_key: str, password: str, target_path: str
    ):
        fs = self.fs_factory()

        with spark_conf_based_temp_folder(self.config) as tmp_dir:
            logger.info(f"Using temporary directory: {tmp_dir}")
            # i.e: /tmp/tmpdir123/Clinical.7z
            archive_path = path.join(tmp_dir, Path(source_key).name)
            # i.e: /tmp/tmpdir123/uncompressed/
            uncompressed_path = path.join(tmp_dir, "uncompressed/")
            logger.info(f"Decompressing locally the file in {archive_path}")

            logger.info("download (7z) archive %s to %s.", source_key, archive_path)
            fs.download_file(source_key, archive_path)

            logger.info(
                "[START] decompress (7z) %s to %s",
                archive_path,
                uncompressed_path,
            )
            with py7zr.SevenZipFile(
                archive_path, mode="r", password=password
            ) as compressed_archiver:
                compressed_archiver.extractall(uncompressed_path)
            logger.info(
                "[COMPLETED] decompress (7z) %s to %s",
                archive_path,
                uncompressed_path,
            )

            self._upload_uncompressed_files(uncompressed_path, target_path)

        return ALL_ENTRIES

    def _upload_uncompressed_files(self, uncompressed_path: str, target_path: str):
        """
        Upload all files from the uncompressed directory to the target path.

        This method recursively walks through the uncompressed directory,
        normalizes file names, and uploads each file to the target location
        while preserving the directory structure.

        Args:
            uncompressed_path (str): Local path to the directory containing uncompressed files.
                Example: "/tmp/tmpdir123/uncompressed/"
            target_path (str): The base path where files will be uploaded.
                Example: "ucb.aetion.com/etl/cprd/20250731/raw/"
        """

        for entry_local_path in Path(uncompressed_path).glob("**/*"):
            if not entry_local_path.is_file():
                logger.warning(
                    "skipping entry %s upload - not a file", entry_local_path
                )
                continue

            entry_local = str(entry_local_path)
            rel_entry_local = os.path.relpath(entry_local, uncompressed_path)

            # normalize entry name to include only a name of the closest
            # parent directory (if exists)
            normalized_entry = path.join(
                Path(rel_entry_local).parent.name,
                self.normalize_entry_name(Path(rel_entry_local).name),
            )
            target_key = path.join(target_path, normalized_entry)

            logger.info("upload (7z) entry %s to %s.", entry_local, target_key)
            fs = self.fs_factory()
            fs.upload_file(entry_local, target_key)


@contextmanager
def spark_conf_based_temp_folder(config: dict[str, str] | None = None):
    """
    Context manager to create a temporary directory based on Spark configuration.

    This function attempts to determine the appropriate temporary directory based on Spark configuration.

    It checks in the following order:

        1. The spark conf 'spark.kubernetes.executor.volumes.persistentVolumeClaim.spark-local-dir-1.mount.path'
        2. The spark conf 'spark.local.dirs'
        3. The environment variable 'SPARK_LOCAL_DIRS'

    If none of the above are found, it defaults to the system's temporary directory.

    Args:
        config (dict[str, str] | None): Optional dictionary containing Spark configuration parameters.

    Yields:
        str: Path to the created temporary directory.

    """
    config = config or {}

    def get_pvc_mount_dir():
        return config.get(
            "spark.kubernetes.executor.volumes.persistentVolumeClaim.spark-local-dir-1.mount.path",
            None,
        )

    def get_spark_local_dirs_conf():
        dirs = config.get("spark.local.dirs", None)
        return dirs.split(",")[0] if dirs else None

    def get_spark_local_dirs_env():
        dirs = os.getenv("SPARK_LOCAL_DIRS", None)
        return dirs.split(",")[0] if dirs else None

    base_path = (
        get_pvc_mount_dir() or get_spark_local_dirs_conf() or get_spark_local_dirs_env()
    )

    if base_path:
        temp_dir = path.join(base_path, "unarchiver_" + str(uuid.uuid4()))
        os.makedirs(temp_dir, exist_ok=True)
        try:
            yield temp_dir
        finally:
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to delete temporary directory {temp_dir}: {e}")
    else:
        # Use system temporary directory
        with tempfile.TemporaryDirectory() as tmp:
            yield tmp


