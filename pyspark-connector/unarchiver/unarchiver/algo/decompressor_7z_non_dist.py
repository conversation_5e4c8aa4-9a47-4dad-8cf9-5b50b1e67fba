from contextlib import contextmanager
import logging
import os
import tempfile
from os import path
from pathlib import Path
from typing import Iterator, Any
from io import IOBase
import uuid
import py7zr
import shutil

from unarchiver.algo.decompressor_base import DecompressorBase
from unarchiver.extended_s3fs import ExtendedS3FileSystem

logger = logging.getLogger(__name__)

ALL_ENTRIES = "_all_"

try:
    import libarchive
except ImportError:
    libarchive = None


class LibArchiveEntryReader(IOBase):
    """
    A file-like object that reads from a libarchive entry in streaming blocks.

    This class provides a file-like interface for reading decompressed content
    from a libarchive entry, enabling memory-efficient streaming of large files.
    Inherits from IOBase to be compatible with upload_stream() method.
    """

    def __init__(self, entry: Any) -> None:
        """
        Initialize the reader with a libarchive entry.

        Args:
            entry: A libarchive entry object that supports get_blocks() method.
        """
        super().__init__()
        self.entry = entry
        self._current_block: bytes = b''
        self._blocks_iterator: Iterator[bytes] | None = None
        self._finished: bool = False

    def readable(self) -> bool:
        """Return True since this is a readable stream."""
        return True

    def read(self, size: int = -1) -> bytes:
        """
        Read up to size bytes from the entry.

        Args:
            size: Maximum number of bytes to read. If -1, read all remaining data.

        Returns:
            bytes: The read data, or empty bytes if EOF reached.
        """
        if self._finished:
            return b''

        if self._blocks_iterator is None:
            self._blocks_iterator = self.entry.get_blocks()

        if size == -1:
            # Read all remaining data
            result = self._current_block
            self._current_block = b''

            try:
                if self._blocks_iterator is not None:
                    for block in self._blocks_iterator:
                        result += block
            except StopIteration:
                pass

            self._finished = True
            return result

        # Read up to 'size' bytes
        while len(self._current_block) < size and not self._finished:
            try:
                if self._blocks_iterator is not None:
                    next_block = next(self._blocks_iterator)
                    self._current_block += next_block
                else:
                    self._finished = True
                    break
            except StopIteration:
                self._finished = True
                break

        if len(self._current_block) <= size:
            result = self._current_block
            self._current_block = b''
            return result
        else:
            result = self._current_block[:size]
            self._current_block = self._current_block[size:]
            return result


class Decompressor7zNotDist(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".7z", ".7zip"]

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key: str, password: str | None) -> list[tuple[str, int]]:
        """
        List entries in the 7z archive.

        For this non-distributed decompressor, we always return a single entry
        representing all files in the archive.

        Args:
            source_key: Path to the source archive (unused for this implementation).
            password: Archive password (unused for this implementation).

        Returns:
            A list containing a single tuple with the entry name and size.
        """
        # Parameters are part of the interface but not used in this implementation
        _ = source_key, password
        return [(ALL_ENTRIES, 0)]




    def decompress_entry(
        self, source_key: str, entry_name: str, password: str, target_path: str
    ):
        """
        Decompress all entries of a 7z archive to a target s3 path.

        Args:
            source_key (str): The path/key to the source 7z archive file.
                Example: "ucb.aetion.com/upload/cprd/20250731/Clinical.7z"
            entry (str): The entry identifier (must be "_all_" for this decompressor).
                Example: "_all_"
            password (str): Password for the encrypted archive, or None/empty string for unencrypted.
                Example: "mypassword123" or ""
            target_path (str): The base path where decompressed files will be uploaded.
                Example: "ucb.aetion.com/etl/cprd/20250731/raw/"

        Returns:
            str: The entry identifier that was processed (always "_all_").
        """
        assert entry_name == ALL_ENTRIES
        logging.info(
            "decompressing %s:%s and uploading to %s",
            source_key,
            entry_name,
            target_path,
        )

        if libarchive:
            self._decompress_all_using_streaming(source_key, password, target_path)
        else:
            self._decompress_all_using_local_filesystem(source_key, password, target_path)
        pass



    def _decompress_all_using_streaming(
        self, source_key: str, password: str | None, target_path: str
    ) -> None:
        """
        Decompress all entries of a 7z archive to a target S3 path using streaming for both input and output.

        This method implements memory-efficient streaming decompression where:
        1. The 7z archive is read from S3 in streaming chunks without loading the entire file into memory
        2. Each archive entry is decompressed and uploaded to S3 in chunks, avoiding loading entire decompressed files into memory
        3. Proper error handling for S3 operations, archive corruption, and authentication failures

        Args:
            source_key: The S3 URL to the source 7z archive file.
                Example: "s3://ucb.aetion.com/upload/cprd/20250731/Clinical.7z"
            password: The password for the 7z archive, or None for unencrypted archives.
                Example: "mypassword123" or None
            target_path: The S3 URL to the base path where decompressed files will be uploaded.
                Example: "s3://ucb.aetion.com/etl/cprd/20250731/raw/"

        Raises:
            ImportError: If libarchive is not available for streaming decompression.
            ValueError: If source_key or target_path are invalid.
            Exception: For S3 connection errors, archive corruption, or authentication failures.
        """
        if libarchive is None:
            raise ImportError(
                "libarchive is required for streaming decompression. "
                "Please install libarchive-c: pip install libarchive-c"
            )

        if not source_key or not target_path:
            raise ValueError("source_key and target_path must be non-empty strings")

        fs: ExtendedS3FileSystem = self.fs_factory()
        download_block_size: int = 8 * 1024 * 1024  # 8MB chunks for reading from S3
        upload_block_size: int = self._get_upload_block_size() or (512 * 1024 * 1024)  # Configurable upload block size with fallback

        logger.info(
            "Starting streaming decompression of %s to %s (download_block_size=%d, upload_block_size=%d)",
            source_key, target_path, download_block_size, upload_block_size
        )

        try:
            with fs.open(source_key, block_size=download_block_size, cache_type="none") as f_in:
                with libarchive.stream_reader(f_in, passphrase=password) as archive:
                    entry_count: int = 0
                    for entry in archive:
                        if entry.isdir():
                            logger.debug("Skipping directory entry: %s", entry.pathname)
                            continue

                        # Normalize entry name and create target path
                        normalized_name: str = self.normalize_entry_name(entry.pathname)
                        target_key: str = path.join(target_path, normalized_name)

                        logger.info("Decompressing and uploading entry %s to %s", entry.pathname, target_key)

                        try:
                            # Create streaming reader for the entry and upload to S3
                            entry_reader = LibArchiveEntryReader(entry)
                            fs.upload_stream(entry_reader, target_key, block_size=upload_block_size)
                            entry_count += 1
                            logger.debug("Successfully uploaded entry %s", entry.pathname)

                        except Exception as entry_error:
                            logger.error(
                                "Failed to process entry %s: %s",
                                entry.pathname, entry_error
                            )
                            # Continue processing other entries instead of failing completely
                            continue

                    logger.info("Completed streaming decompression. Processed %d entries.", entry_count)

        except ImportError:
            # Re-raise ImportError as-is
            raise
        except ValueError:
            # Re-raise ValueError as-is
            raise
        except Exception as e:
            logger.error("Failed to decompress archive %s: %s", source_key, e)
            raise Exception(f"Archive decompression failed for {source_key}: {e}") from e



    def _decompress_all_using_local_filesystem(
        self, source_key: str, password: str, target_path: str
    ):
        fs = self.fs_factory()

        with spark_conf_based_temp_folder(self.config) as tmp_dir:
            logger.info(f"Using temporary directory: {tmp_dir}")
            # i.e: /tmp/tmpdir123/Clinical.7z
            archive_path = path.join(tmp_dir, Path(source_key).name)
            # i.e: /tmp/tmpdir123/uncompressed/
            uncompressed_path = path.join(tmp_dir, "uncompressed/")
            logger.info(f"Decompressing locally the file in {archive_path}")

            logger.info("download (7z) archive %s to %s.", source_key, archive_path)
            fs.download_file(source_key, archive_path)

            logger.info(
                "[START] decompress (7z) %s to %s",
                archive_path,
                uncompressed_path,
            )
            with py7zr.SevenZipFile(
                archive_path, mode="r", password=password
            ) as compressed_archiver:
                compressed_archiver.extractall(uncompressed_path)
            logger.info(
                "[COMPLETED] decompress (7z) %s to %s",
                archive_path,
                uncompressed_path,
            )

            self._upload_uncompressed_files(uncompressed_path, target_path)

        return ALL_ENTRIES

    def _upload_uncompressed_files(self, uncompressed_path: str, target_path: str):
        """
        Upload all files from the uncompressed directory to the target path.

        This method recursively walks through the uncompressed directory,
        normalizes file names, and uploads each file to the target location
        while preserving the directory structure.

        Args:
            uncompressed_path (str): Local path to the directory containing uncompressed files.
                Example: "/tmp/tmpdir123/uncompressed/"
            target_path (str): The base path where files will be uploaded.
                Example: "ucb.aetion.com/etl/cprd/20250731/raw/"
        """

        for entry_local_path in Path(uncompressed_path).glob("**/*"):
            if not entry_local_path.is_file():
                logger.warning(
                    "skipping entry %s upload - not a file", entry_local_path
                )
                continue

            entry_local = str(entry_local_path)
            rel_entry_local = os.path.relpath(entry_local, uncompressed_path)

            # normalize entry name to include only a name of the closest
            # parent directory (if exists)
            normalized_entry = path.join(
                Path(rel_entry_local).parent.name,
                self.normalize_entry_name(Path(rel_entry_local).name),
            )
            target_key = path.join(target_path, normalized_entry)

            logger.info("upload (7z) entry %s to %s.", entry_local, target_key)
            fs = self.fs_factory()
            fs.upload_file(entry_local, target_key)


@contextmanager
def spark_conf_based_temp_folder(config: dict[str, str] | None = None):
    """
    Context manager to create a temporary directory based on Spark configuration.

    This function attempts to determine the appropriate temporary directory based on Spark configuration.

    It checks in the following order:

        1. The spark conf 'spark.kubernetes.executor.volumes.persistentVolumeClaim.spark-local-dir-1.mount.path'
        2. The spark conf 'spark.local.dirs'
        3. The environment variable 'SPARK_LOCAL_DIRS'

    If none of the above are found, it defaults to the system's temporary directory.

    Args:
        config (dict[str, str] | None): Optional dictionary containing Spark configuration parameters.

    Yields:
        str: Path to the created temporary directory.

    """
    config = config or {}

    def get_pvc_mount_dir():
        return config.get(
            "spark.kubernetes.executor.volumes.persistentVolumeClaim.spark-local-dir-1.mount.path",
            None,
        )

    def get_spark_local_dirs_conf():
        dirs = config.get("spark.local.dirs", None)
        return dirs.split(",")[0] if dirs else None

    def get_spark_local_dirs_env():
        dirs = os.getenv("SPARK_LOCAL_DIRS", None)
        return dirs.split(",")[0] if dirs else None

    base_path = (
        get_pvc_mount_dir() or get_spark_local_dirs_conf() or get_spark_local_dirs_env()
    )

    if base_path:
        temp_dir = path.join(base_path, "unarchiver_" + str(uuid.uuid4()))
        os.makedirs(temp_dir, exist_ok=True)
        try:
            yield temp_dir
        finally:
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to delete temporary directory {temp_dir}: {e}")
    else:
        # Use system temporary directory
        with tempfile.TemporaryDirectory() as tmp:
            yield tmp


