import logging
from os import path
from pathlib import Path

from unarchiver.algo.decompressor_base import DecompressorBase

logger = logging.getLogger(__name__)


class DecompressorCopy(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".csv", ".txt", ".dat", ".sas7bdat"]

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key, password):
        fs = self.fs_factory()
        obj_size = fs.info(source_key)["size"]

        entries = [(Path(source_key).name, obj_size)]

        logger.info(
            "a list of (%d) entries (copy) for %s\n%s",
            len(entries),
            source_key,
            entries,
        )

        return entries

    def decompress_entry(self, source_key, entry, password, target_path):
        fs = self.fs_factory()

        normalized_entry = self.normalize_entry_name(entry)
        target_key = path.join(target_path, entry)

        logger.info("decompress (copy) entry %s to %s.", entry, normalized_entry)
        with fs.open(source_key, "rb") as f_in:
            fs.upload_stream(f_in, target_key, block_size=self._get_upload_block_size())

        return normalized_entry
