import os
import tarfile
import logging
from os import path

from unarchiver.algo.decompressor_base import DecompressorBase

logger = logging.getLogger(__name__)

ALL_ENTRIES = "_all_"


def parallel_upload(fs, uncompressed_file, target_path):
    logger.info("[START] uploading to %s", target_path)
    fs.upload_file(uncompressed_file, target_path)
    os.remove(uncompressed_file)
    logger.info("[COMPLETE] uploading to %s", target_path)


class DecompressorTar(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".tar", ".tar.gz", ".tar.xz", ".tar.bz2", ".tgz", ".txz", ".tbz2"]

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)
        self.parallelism = int(config.get("aetion.unarchiver.tar.parallelism", 1))
        self.skip_existing = (
            config.get("aetion.unarchiver.tar.skip_existing", "false").lower() == "true"
        )
        logger.info("parallelism: %d", self.parallelism)

    def list_entries(self, source_key, password):
        return [(part_no, 0) for part_no in range(self.parallelism)]

    def decompress_entry(self, source_key, entry, password, target_path):
        fs = self.fs_factory()
        current_file_idx = 0
        my_part = int(entry)
        with fs.open(source_key, "rb") as f_in, tarfile.open(
            fileobj=f_in, mode="r|*"
        ) as tar:
            for member in tar:
                if member.isfile():
                    if current_file_idx % self.parallelism != my_part:
                        current_file_idx += 1
                        continue
                    target_key = path.join(
                        target_path, self.normalize_entry_name(str(member.name))
                    )
                    logger.info("extracting entry %s", member.name)
                    if self.skip_existing and fs.exists(target_key):
                        logger.info("entry %s already exists, skipping", target_key)
                        continue
                    with tar.extractfile(member) as f_tar_entry:
                        fs.upload_stream(
                            f_tar_entry,
                            target_key,
                            block_size=self._get_upload_block_size(),
                        )
                    current_file_idx += 1

            return ALL_ENTRIES
