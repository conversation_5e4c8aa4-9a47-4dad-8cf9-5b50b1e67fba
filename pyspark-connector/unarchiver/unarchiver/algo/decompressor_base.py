import logging

logger = logging.getLogger(__name__)

DEFAULT_UPLOAD_BLOCK_SIZE = 512 * 1024 * 1024  # Default to 512 MB


class DecompressorBase:
    @staticmethod
    def normalize_entry_name(entry_name):
        return entry_name.replace(" ", "_")

    @staticmethod
    def get_supported_ext():
        raise NotImplementedError()

    def __init__(self, fs_factory, config: dict | None = None):
        self.fs_factory = fs_factory
        self.config = config or {}

    def list_entries(self, source_key, password):
        raise NotImplementedError()

    def decompress_entry(self, source_key, entry_name, password, target_path):
        raise NotImplementedError()

    def _get_upload_block_size(self) -> int | None:

        _AETION_KEY = "aetion.unarchiver.upload_block_size"
        _SPARK_KEY = "spark.aetion.unarchiver.upload_block_size"

        block_size = None

        if _AETION_KEY in self.config:
            block_size = self.config[_AETION_KEY]
            logger.info(f"Found block size in config {_AETION_KEY}: {block_size}")
        elif _SPARK_KEY in self.config:
            block_size = self.config[_SPARK_KEY]
            logger.info(f"Found block size in config {_SPARK_KEY}: {block_size}")
        else:
            block_size = DEFAULT_UPLOAD_BLOCK_SIZE

        try:
            if isinstance(block_size, str):
                block_size = _convert_to_bytes(block_size)
        except (ValueError, TypeError):
            logger.warning("Invalid block size '%s', using default value.", block_size)
            block_size = DEFAULT_UPLOAD_BLOCK_SIZE
        finally:
            if block_size <= 0:
                logger.warning("Block size must be positive, using default value.")
                block_size = DEFAULT_UPLOAD_BLOCK_SIZE

        logger.info(
            "Using upload block size: %d bytes (%d MB)",
            block_size,
            block_size // (1024 * 1024),
        )

        return block_size


def _convert_to_bytes(block_size_spec: str) -> int:
    """Convert a block size specification string to bytes."""
    block_size_spec = block_size_spec.strip().lower()
    if block_size_spec.endswith("m"):
        return int(block_size_spec[:-1]) * 1024 * 1024
    elif block_size_spec.endswith("k"):
        return int(block_size_spec[:-1]) * 1024
    elif block_size_spec.endswith("g"):
        return int(block_size_spec[:-1]) * 1024 * 1024 * 1024
    else:
        return int(block_size_spec) * 1024 * 1024  # Assume it's already in megabytes
