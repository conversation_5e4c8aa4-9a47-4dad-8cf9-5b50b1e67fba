import logging
from os import path
from pathlib import Path
from zipfile import ZipFile

from unarchiver.algo.decompressor_base import DecompressorBase

logger = logging.getLogger(__name__)


class DecompressorZip(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".zip"]

    def __init__(self, fs_factory, config=None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key, password):
        fs = self.fs_factory()

        with fs.open(source_key, "rb") as f_zip:
            with ZipFile(f_zip) as archive:
                entries = [
                    (entry.filename, entry.file_size) for entry in archive.infolist()
                ]

                logger.info(
                    "a list of (%d) entries (zip) for %s\n%s",
                    len(entries),
                    source_key,
                    entries,
                )

                return entries

    def decompress_entry(self, source_key, entry, password, target_path):
        fs = self.fs_factory()

        with fs.open(source_key, "rb") as f_zip:
            with ZipFile(f_zip) as archive:
                entry_info = archive.getinfo(entry)
                normalized_entry = self.normalize_entry_name(
                    Path(entry_info.filename).name
                )
                target_key = path.join(target_path, normalized_entry)

                pwd = None if not password else password.encode()

                logger.info(
                    "decompress (zip) entry %s:%s to %s.",
                    source_key,
                    entry,
                    normalized_entry,
                )

                with archive.open(entry_info, pwd=pwd) as f_in:
                    fs.upload_stream(
                        f_in, target_key, block_size=self._get_upload_block_size()
                    )

                return normalized_entry
