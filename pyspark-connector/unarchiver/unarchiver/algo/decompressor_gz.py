import gzip
import logging
from os import path
from pathlib import Path

from unarchiver.algo.decompressor_base import DecompressorBase

logger = logging.getLogger(__name__)

_FILE_SIZE_THRESHOLD = 1000 * 1024 * 1024  # 1GB


class DecompressorGz(DecompressorBase):
    @staticmethod
    def get_supported_ext():
        return [".gz", ".gzip"]

    def __init__(self, fs_factory, config: dict | None = None):
        DecompressorBase.__init__(self, fs_factory, config)

    def list_entries(self, source_key, password):
        fs = self.fs_factory()

        compressed_obj_size = fs.info(source_key)["size"]
        # assumption: compression ratio is <10
        uncompressed_obj_size = compressed_obj_size * 20

        entries = [(Path(source_key).stem, uncompressed_obj_size)]

        logger.info(
            "a list of (%d) entries (gz) for %s\n%s",
            len(entries),
            source_key,
            entries,
        )

        return entries

    def decompress_entry(self, source_key, entry, password, target_path):
        fs = self.fs_factory()

        with fs.open(source_key, "rb") as f_zip, gzip.open(f_zip) as f_in:
            normalized_entry = self.normalize_entry_name(entry)
            target_key = path.join(target_path, normalized_entry)

            logger.info(
                "decompress (gz) entry %s:%s to %s.",
                source_key,
                entry,
                target_key,
            )
            fs.upload_stream(f_in, target_key, block_size=self._get_upload_block_size())

        return normalized_entry
