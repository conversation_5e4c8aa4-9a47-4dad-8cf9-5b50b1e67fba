from io import IOBase
import os
import boto3
import s3fs
import logging

from boto3.s3.transfer import TransferConfig

DEFAULT_UPLOAD_BLOCK_SIZE = 256 * 1024 * 1024  # Default to 256 MB


class ExtendedS3FileSystem(s3fs.S3FileSystem):

    def __init__(self, *args, **kwargs):
        s3fs.S3FileSystem.__init__(self, *args, **kwargs)

    def download_file(self, path, filename, **kwargs):
        bucket, key, *_ = self.split_path(path)
        s3 = boto3.client("s3")
        s3.download_file(bucket, key, filename)

    def upload_file(self, filename, path, **kwargs):
        bucket, key, *_ = self.split_path(path)
        s3 = boto3.client("s3")
        config = generate_transfer_config(filename)
        logging.info(
            f"Uploading {filename} to s3://{bucket}/{key} with config: {config}"
        )
        s3.upload_file(filename, bucket, key, Config=config)

    def upload_stream(
        self,
        f_in: IOBase,
        target_key: str,
        block_size=DEFAULT_UPLOAD_BLOCK_SIZE,
        **kwargs,
    ):

        logging.info("Starting multipart upload for %s", target_key)
        logging.info("Using upload block size: %d bytes (%d MB)", block_size, block_size // (1024 * 1024))

        s3 = boto3.client("s3")
        bucket, key, *_ = self.split_path(target_key)

        mpu = s3.create_multipart_upload(Bucket=bucket, Key=key)
        upload_id = mpu["UploadId"]
        parts = []
        part_number = 1

        while True:
            data = f_in.read(block_size)
            if not data:
                break

            logging.info("Uploading part %d of %s to %s", part_number, key, bucket)
            response = s3.upload_part(
                Bucket=bucket,
                Key=key,
                PartNumber=part_number,
                UploadId=upload_id,
                Body=data,
            )
            parts.append(
                {
                    "PartNumber": part_number,
                    "ETag": response["ETag"],
                }
            )
            part_number += 1

        if parts:
            logging.info("Completing multipart upload for %s", key)
            s3.complete_multipart_upload(
                Bucket=bucket,
                Key=key,
                UploadId=upload_id,
                MultipartUpload={"Parts": parts},
            )
        else:
            error_msg = f"{target_key} is empty. No S3 object will be created."
            logging.error(error_msg)
            raise ValueError(error_msg)


_DEFAULT_CONFIG = TransferConfig(
    multipart_threshold=1024 * 1024 * 5,  # 5 MB threshold for multipart uploads
    multipart_chunksize=1024 * 1024 * 256,  # 256 MB parts,
    max_concurrency=10,  # Number of threads to use for multipart uploads
)


def generate_transfer_config(filename: str) -> TransferConfig:
    """
    Generates a TransferConfig object for managing file transfer settings based on the size of the specified file.
    This function determines the optimal multipart chunk size and other transfer parameters for uploading or downloading
    files efficiently. If the file size cannot be determined, a default transfer configuration is returned.
    Args:
        filename (str): The path to the file for which the transfer configuration is to be generated.
    Returns:
        TransferConfig: A configuration object specifying multipart threshold, chunk size, and concurrency settings
        for file transfers.
    """

    obj_size = get_file_size(filename)

    if obj_size is None:
        logging.warning(
            f"Could not determine size for file: {filename}. Using default transfer config."
        )
        return _DEFAULT_CONFIG

    block_size = get_block_size(obj_size)

    return TransferConfig(
        multipart_threshold=1024 * 1024 * 5,  # 5 MB threshold for multipart uploads
        multipart_chunksize=block_size,  # Use the computed block size
        max_concurrency=10,  # Number of threads to use for multipart uploads
    )


def get_block_size(
    uncompressed_obj_size: int,
    default_block_size: int = 1024 * 1024 * 256,
    max_s3_parts: int = 10000,
):
    """
    Compute the optimal block size for S3 uploads based on the uncompressed object size.

    :param uncompressed_obj_size: Size of the uncompressed object in bytes.
    :param default_block_size: Default block size in bytes.
    :param max_s3_parts: Maximum number of parts allowed in S3 multipart upload.
    :return: Optimal block size in bytes.
    """
    if not uncompressed_obj_size:
        return default_block_size

    if uncompressed_obj_size / default_block_size < max_s3_parts:
        return default_block_size

    return int(uncompressed_obj_size / max_s3_parts)


def get_file_size(filename: str) -> int | None:
    """
    Get the size of a file in bytes. The file is in the local filesystem.

    :param filename: Path to the file.
    :return: Size of the file in bytes or None if an error occurs.
    """

    try:
        return os.path.getsize(filename)
    except Exception as e:
        logging.error(f"Error getting file size for {filename}: {e}")
        return None
