from typing import Optional
import importlib.resources
import logging

logger = logging.getLogger(__name__)

_PACKAGE = "unarchiver"
_ARTIFACT_NAME = "Unarchiver"


def fetch_git_version() -> Optional[str]:
    """Fetch the git sha version from the GIT_VERSION file inside the packaged wheel.

    Returns:
        Optional[str]: The git sha version if it exists, otherwise None.
    """
    try:
        return importlib.resources.read_text(_PACKAGE, "GIT_VERSION").strip()
    except Exception:
        logger.warning(
            "GIT_VERSION file not found... We cannot resolve the git sha version."
        )
        return None


def log_version(prefix: Optional[str] = None) -> None:
    """Log the version of the package.

    Args:
        prefix (Optiona[str], optional): A prefix to add to the log message. Defaults to None.
    """
    version = fetch_git_version() or "Unknown"
    prefix = f"{prefix} " if prefix is not None else ""
    logger.info(f"{prefix}version: {version}")


def configure_logging(level: int = logging.INFO) -> None:
    """Configure the logging system.

    Args:
        level (int, optional): The logging level. Defaults to logging.INFO.
    """
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=level
    )
    logger.info("Logging system configured.")


if __name__ == "__main__":
    configure_logging()
    log_version(_ARTIFACT_NAME)
