from os import path

import itertools
import logging
import sys
import traceback
from pathlib import Path
from pyspark import SparkContext
from pyspark.sql import SparkSession
from typing import Optional, Callable, Any

from unarchiver.algo.decompressor_base import DecompressorBase
from unarchiver.extended_s3fs import ExtendedS3FileSystem
from unarchiver.version import log_version
from unarchiver.logging.config import setup as setup_logging

logger = logging.getLogger(__name__)

DATA_SECRET_FILE = "data_secret.pwd"


def _setup_executor_logging():
    """
    Set up logging for the executor to ensure that each executor has its own logger instance.
    """
    import logging

    if not logger.hasHandlers():
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            force=True,
        )


def compute_optimal_block_size(uncompressed_obj_size):
    # 5Mb
    default_block_size = 5 * 2**20

    if not uncompressed_obj_size:
        return default_block_size

    # UploadPart operation: Part number must be an integer between
    # 1 and 10000, inclusive
    max_s3_parts = 990

    if uncompressed_obj_size / default_block_size < max_s3_parts:
        return default_block_size

    return int(uncompressed_obj_size / max_s3_parts)


def fs_factory_default():
    fs = ExtendedS3FileSystem(s3_additional_kwargs={"ServerSideEncryption": "AES256"})
    fs.read_timeout = 600
    fs.default_fill_cache = False
    return fs


class Unarchiver:
    def __init__(
        self,
        source_path: str,
        target_path: str,
        password: Optional[str] = None,
        fs_factory: Callable[[], Any] = fs_factory_default,
    ):
        """
        Copies files around S3.

        Unarchive and copy files from the raw/ folder to the etl/ folder. Uses
        Spark and Databricks to parralelize decompression.

        :param source_path: The path to look in for raw compressed folders.
        :type source_path: str
        :example source_path: 'lilly.aetion.com/upload/flatiron_testing/201906/'
        :param target_path: The path to copy files to when unarchived.
        :type target_path: str
        :example target_path: 'lilly.aetion.com/etl/flatiron_testing/201906/raw/'
        :param password: Password to use for the s3fs to connect to s3.
        :type password: Optional[str]
        :param fs_factory: What to use as the filesystem factory.
        :type fs_factory: Callable[[],AbstractFileSystem]
        """
        self.source_path = source_path
        self.target_path = target_path
        self.password = password
        self.fs_factory = fs_factory
        self.config: dict[str, str | Any] = {}  # lazy init

        self.ext_algo_map = {
            ext: algo
            for algo in DecompressorBase.__subclasses__()
            for ext in algo.get_supported_ext()
        }

    def decompress_all_dist(self, sc: SparkContext):
        """
            Decompress all
        :param sc:
        :return: [(entries_success, errors, warns)]

                 entries_success: [(source_key, entry)]
                 errors: [(source_key, entry, err)]
                 warns: [(source_key, entry, warn)]
        """

        fs = self.fs_factory()
        self.config = {
            k: v
            for (k, v) in sc.getConf().getAll()
        }

        if not self.password and fs.exists(
            path.join(self.source_path, DATA_SECRET_FILE)
        ):
            logger.info("retrieve data secret from %s", DATA_SECRET_FILE)
            with fs.open(path.join(self.source_path, DATA_SECRET_FILE), "r") as f:
                self.password = f.read()

        source_keys = fs.find(self.source_path)

        logger.info(
            "decompress_all_dist (%d) files: \n%s",
            len(list(source_keys)),
            "\n".join(source_keys),
        )

        # list of archives entries (parallelized per archive)
        listing = (
            sc.parallelize(source_keys)
            .map(lambda key: self.list_entries(key))
            .collect()
        )
        logger.info("all archives listed (%d) entries:\n%s", len(listing), listing)

        listing_errors = [
            (source_key, None, err) for source_key, entries, err, warn in listing if err
        ]
        listing_warns = [
            (source_key, None, warn)
            for source_key, entries, err, warn in listing
            if warn
        ]

        # flatten list of source key and entry
        listing_entries = list(
            itertools.chain.from_iterable(
                [
                    itertools.product((source_key,), entries)
                    for source_key, entries, err, warn in listing
                    if not err and not warn
                ]
            )
        )

        # uncompress all archives entries (parallelized per archive entry)
        entries_all = (
            sc.parallelize(listing_entries)
            .map(lambda key: self.decompress(key[0], key[1]))
            .collect()
        )
        entries_success = [
            (source, entry)
            for source, entry, err, warn in entries_all
            if not err and not warn
        ]
        entries_errors = [
            (source, entry, err) for source, entry, err, warn in entries_all if err
        ]
        entries_warns = [
            (source, entry, warn) for source, entry, err, warn in entries_all if warn
        ]

        logger.info(
            "decompress_all_dist (%d) files completed: \n%s\nerrors:\n%s\nwarns:\n%s",
            len(list(source_keys)),
            entries_success,
            listing_errors + entries_errors,
            listing_warns + entries_warns,
        )

        return (
            entries_success,
            listing_errors + entries_errors,
            listing_warns + entries_warns,
        )

    def list_entries(self, source_key):
        """

        :param source_key:
        :return: [(source_key, entries, err, warn)]
        """
        _setup_executor_logging()
        logger.info("listing entries for: %s", source_key)

        source_ext = self._get_source_file_ext(source_key)

        algo = self.ext_algo_map.get(source_ext, None)
        if not algo:
            warn = "Unsupported archive: {}. skipping.".format(source_key)
            logger.warning(warn)
            return source_key, [], None, warn

        try:
            entries = algo(self.fs_factory, self.config).list_entries(
                source_key, self.password
            )
            return source_key, entries, None, None
        except Exception as ex:
            logger.error(
                "failed to list entries with %s handler (%s): %s\n%s\n%s",
                source_ext,
                algo.__name__,
                source_key,
                ex,
                traceback.format_exc(),
            )
            logger.error(traceback.format_stack())
            return source_key, [], ex, None

    @staticmethod
    def _get_source_file_ext(source_key):
        file_suffixes = Path(source_key).suffixes[-2:]
        source_ext = "".join(
            file_suffixes if file_suffixes[:1] == [".tar"] else file_suffixes[-1:]
        )
        return source_ext

    def decompress(self, source_key, entry):
        """
            Decompress a single s3 object. This runs inside a Spark executor.
        :param source_key:
        :param entry:
        :return: tuple: (source_key, entry, err, warn)
        """

        # We must set up logging inside the executor to ensure that each executor has its own logger instance.
        _setup_executor_logging()

        (entry_name, size) = entry

        logger.info("decompressing: %s:%s (size: %s)", source_key, entry_name, size)
        source_ext = self._get_source_file_ext(source_key)

        algo = self.ext_algo_map.get(source_ext, None)
        if not algo:
            warn = "Unsupported archive: {}:{} skipping.".format(source_key, entry_name)
            logger.warning(warn)
            return source_key, entry_name, None, warn

        block_size = compute_optimal_block_size(size)

        def fs_factory_ext():
            fs = self.fs_factory()
            fs.default_block_size = block_size
            return fs

        parents = str(Path(source_key[len(self.source_path):]).parent)
        target_path_with_dir = (
            self.target_path
            if parents == "."
            else path.join(self.target_path, parents) + "/"
        )

        try:
            logger.info(
                "[START] decompress with %s algo (%s): %s:%s (size: %d), default_block_size: %d",
                algo.__name__,
                source_ext,
                source_key,
                entry_name,
                size,
                block_size,
            )
            normalized_entry = algo(fs_factory_ext, self.config).decompress_entry(
                source_key, entry_name, self.password, target_path_with_dir
            )
            logger.info(
                "[COMPLETE] decompress with %s algo: %s:%s",
                algo.__name__,
                source_key,
                entry_name,
            )
        except Exception as ex:
            logger.error(
                "[FAILED] decompress with %s algo: %s:%s\n%s\n%s",
                algo.__name__,
                source_key,
                entry_name,
                ex,
                traceback.format_exc(),
            )
            logger.error(traceback.format_stack())
            return source_key, entry_name, ex, None

        return source_key, normalized_entry, None, None


def main(source_path, target_path, password):
    setup_logging()
    log_version(__name__)
    unarchiver = Unarchiver(source_path, target_path, password)
    sc = SparkSession.builder.appName("Unarchiver").getOrCreate().sparkContext
    entries_success, errors, warns = unarchiver.decompress_all_dist(sc)

    if warns:
        logger.warning("decompress_all_dist completed with warns: \n%s", warns)

    if errors:
        raise Exception(
            "decompress_all_dist completed with errors: \n{}".format(errors)
        )


if __name__ == "__main__":
    """
    enable local testing with an invocation such as
    python -m unarchiver.unarchiver some_upload_bucket_from_a_pipeline_var some_scratch_destination

    note that this will download & upload all data
    """
    main(sys.argv[1], sys.argv[2], None)
