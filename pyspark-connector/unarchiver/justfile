# Set default shell with strict flags
set shell := ["bash", "-euo", "pipefail", "-c"]

CURR_VERSION := `uv run --no-project -p 3.12 - <<< "import tomllib; print(tomllib.load(open('pyproject.toml', 'rb'))['project']['version'])"`
PYTHON_VERSION := `cat .python-version | sed 's/^\(3\.[0-9]*\)\..*/\1/'`
AET_GHA_SCRIPTS_FOLDER := join(justfile_directory(), "..", "..",  ".github", "scripts")
COMPONENT := "unarchiver"

default:
    just --list

# Create a virtual environment using uv
venv:
    @echo "*** Creating virtual environment using uv in ./.venv... ***"
    @[[ -d .venv ]] || uv venv --seed -p $(<.python-version)
    @echo "*** Activate the virtual environment with: source .venv/bin/activate ***"

# Setup the environment for development
setup: setup-dev

# Install dependencies in the virtual environment for CICD
setup-ci: venv
    @source .venv/bin/activate; \
    uv pip install '.[ci, dev]'


# Install dependencies in the virtual environment for development
setup-dev: venv
    @source .venv/bin/activate; \
    uv pip install -e '.[ci, dev]'

# List dependencies in the virtual environment
list-deps:
    @echo "*** Listing dependencies... ***"
    uv pip list

# Clean the repository, excluding the virtual environment
clean:
    @echo "*** Cleaning the repo (excluding .venv)... ***"
    rm -rf $(find . -name __pycache__)
    rm -rf .pytest_cache
    rm -rf spark-warehouse
    rm -rf target
    rm -rf dist
    rm  -rf build
    rm -rf *.egg-info

# Clean the virtual environment
clean-venv:
    rm -rf .venv

# Clean both the repository and the virtual environment
clean-all: clean clean-venv
    @echo "*** Cleaned the repository and virtual environment ***"

# format the code using black
format:
    @echo "*** Formatting code with black... ***"
    @source .venv/bin/activate; \
    black --line-length 120 unarchiver tests

flake:
    @echo "*** Running flake8... ***"
    @source .venv/bin/activate; \
    flake8 unarchiver tests --exclude=.venv --ignore="E501,W503,E126,W504"

mypy:
    @echo "*** Running mypy... ***"
    @source .venv/bin/activate; \
    mypy unarchiver tests --exclude=.venv --ignore-missing-imports

check: flake mypy
    @echo "*** Code checks completed ***"

# Run unit and integration tests
test:
    @echo "*** Running unit tests... ***"
    @source .venv/bin/activate; \
    python -m pytest  --verbose --show-capture=all -vv tests


_build_wheel:
    @echo "*** Building wheel... ***"
    @uv build .

_build_egg:
    @echo "*** Building egg... ***"
    @source .venv/bin/activate; \
    python setup.py bdist_egg

# Build the wheel and egg packages
buildonly: _build_wheel _build_egg
    @echo "*** Build completed ***"


# Print the current version
print-version:
    @echo "{{CURR_VERSION}}"

# Generate the git version file
generate-git-version:
    @[ -d dist ] || mkdir dist;
    @echo "*** Generating GIT_VERSION file with specified version {{CURR_VERSION}}... ***"; \
    uv run --no-project -p {{PYTHON_VERSION}} {{AET_GHA_SCRIPTS_FOLDER}}/gen-git_version.py --version {{CURR_VERSION}} > dist/GIT_VERSION;


git-version:
    @cd dist && ls *.{whl,egg} | xargs -I % zip -r % GIT_VERSION
    @git_version=$(cat dist/GIT_VERSION); \
    echo "*** Added GIT_VERSION file to the package: ${git_version} ***"; \

# Set the version in pyproject.toml
set-version version="None":
    @if [ "{{version}}" = "None" ]; then \
        echo "Error: VERSION missing. Use 'just set-version <NEW_VERSION>'"; \
        exit 1; \
    fi
    @echo "*** Setting version to {{version}}... ***"
    @uv run --no-project -p {{PYTHON_VERSION}} {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py set-version "{{version}}"


# Bump version to the next release version
bump-release:
    @echo "*** Bumping release version... ***"
    uv run --no-project -p {{PYTHON_VERSION}} {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py version-bump

# Bump version to the pre-release version
bump-prerelease:
    @echo "*** Bumping pre-release version... ***"
    uv run --no-project -p {{PYTHON_VERSION}} {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py version-bump --prerelease

# Generate the next release version
generate-next-release-version:
    @echo "*** Generating next release version... ***"
    uv run --no-project -p {{PYTHON_VERSION}} {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py version-bump --dry-run

# Publish the package to PyPI if the version does not already exist
publish:
    count=$(curl -s --netrc -X GET 'https://nexus.eng.aetion.com/service/rest/v1/search?name=unarchiver' | jq --arg ver "{{CURR_VERSION}}" '[.items[] | select(.version == $ver)] | length'); \
    if [[ "${count}" -eq "0" ]]; then \
        echo "*** Publishing v{{CURR_VERSION}} to PyPI... ***"; \
        uv run --no-project -p {{PYTHON_VERSION}} --with twine==6.0.1 twine upload -r aetion-nexus dist/*.{whl,egg,gz}; \
    else \
        echo "Version {{CURR_VERSION}} already exists in PyPI. Skipping upload."; \
    fi;

generate-changelog:
    @uv run --no-project -p {{PYTHON_VERSION}} -s {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py changelog-generate {{COMPONENT}}

update-changelog:
    @uv run --no-project -p {{PYTHON_VERSION}} -s {{AET_GHA_SCRIPTS_FOLDER}}/cicd.py changelog-update {{COMPONENT}} --changelog-file {{justfile_directory()}}/CHANGELOG.md