# Release v0.0.4 (from v0.0.3)

2025-07-11: fix(ci): uarchiver| build-n-deploy| add envar PYPIRC_CONTENT and others (#4639) (c8814b3)
2025-07-11: fix(justfile): remove echo from publish recipe in pypthon projects (#4638) (f775846)

# Release v0.0.3 (from v0.0.2)

2025-07-11: feat(unarchiver): PE-6071: configurable upload block size (#4620) (c225335)
2025-07-08: feat(unarchiver): PE-4835: File Uploads with S3 Multipart Streaming (zip, tgz, tar) (#4595) (478067d)
2025-07-04: refactor(unarchiver): remove library pylzma (#4587) (2476559)
2025-07-02: refactor(gha): add new adip airflow envs 'adip<USER>' (#4580) (f73c356)
2025-07-01: PE-6003: unarchiver. entity too large (#4574) (3379fdc)
2025-06-27: refactor(unarchiver): use cicd python script in the justfile (#4555) (b61f11f)
2025-06-26: ci(unarchiver): fix deploy manually (#4534) (2f71c17)

## Release v0.0.2 (from v0.0.1)

- 2025-02-10: PE-4783: log gitsha version in sparkapps (#3926) (bd3104c)
- 2025-02-11: make existing files check optional (#3939) (b301163)
- 2025-02-26: v1 of spark 3.5.4 dockerfile (#3846) (28e93ac)
- 2025-03-11: PE-4801: Integration aetion logging framework in unarchiver (#4015) (c290c25)
- 2025-03-11: Revert "PE-4801: Integration aetion logging framework in unarchiver (#4015)" (#4017) (e5efcea)
- 2025-03-13: feat(unarchiver): PE-4801: add logging configuration (#4028) (e9a7fe2)
- 2025-03-14: PE-4801: fix(unarchiver): remove reconfiguring logging in decompressors (#4030) (b602f1f)
- 2025-06-26: Pe 5631/unarchiver/add pyproject (#4515) (3835592)
- 2025-06-26: ci(unarchiver): build-n-deploy| add working dir (#4516) (3f2d3d3)
- 2025-06-26: ci(unarchiver): PE-5631: add GHA for release PR creation (#4524) (b6de8e7)