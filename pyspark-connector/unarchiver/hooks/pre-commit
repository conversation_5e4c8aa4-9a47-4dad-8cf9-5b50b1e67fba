#!/bin/bash

# Git only lets us have one pre-commit hook. This file looks at the 
# .git/hooks/pre-commit.d folder, and runs any files under it with the
# executable bit set.

# As per the [documentation](https://git-scm.com/docs/githooks) git changes
# it's working directory to the root of the working tree before running the
# pre-commit hook.

if [ ! -d ".git/hooks/pre-commit.d" ]; then
	echo "pre-commit.d does not exist, did you copy the pre-commit file\
instead of linking the hooks folder?"
	exit 1
fi

for hook in $(find .git/hooks/pre-commit.d -perm -111 -type f | sort)
do
	bash $hook
	RESULT=$?
	if [ $RESULT != 0 ]; then
		echo "pre-commit.d/$hook returned non-zero: $RESULT, aborting"
		exit $RESULT
	fi
done

exit 0
