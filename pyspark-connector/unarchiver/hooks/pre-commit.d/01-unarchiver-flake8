#!/bin/bash

VENV_TEMP=$(mktemp -d)
trap 'rm -rf "$VENV_TEMP"' EXIT
uv venv -p 3.12 ${VENV_TEMP}
source ${VENV_TEMP}/bin/activate
uv pip install black flake8

# Get only files that have been modified, and are ready to be committed
python_files=$(git diff --cached --name-only --diff-filter=ACM | grep ".py$")
python_files=$(find . -name "*py")

for file in $python_files
do
	# Attempt to automatically format the file
	tmpfile=$(mktemp)
	black --line-length 120 --diff $file > $tmpfile
	if [ -s $tmpfile ]
	then
		patch -p0 < $tmpfile
		git add $file
	fi

	# Make sure our file is ready
	flake8 $file

	# And if it's not, fail out.
	RESULT=$?
	if [ $RESULT != 0 ]; then
		echo "Failed to lint $file, aborting"
		exit $RESULT
	fi
done
