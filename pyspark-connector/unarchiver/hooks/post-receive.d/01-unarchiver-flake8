#!/bin/bash

VENV_TEMP=$(mktemp -d)
trap 'rm -rf "$VENV_TEMP"' EXIT
uv venv -p 3.12 ${VENV_TEMP}
source ${VENV_TEMP}/bin/activate
uv pip install flake8

python_files=$(find pyspark-connector/unarchiver -path pyspark-connector/unarchiver/.venv -prune -o -name '*.py' -print)

for file in $python_files
do
	flake8 --append-config=pyspark-connector/unarchiver/setup.cfg $file
	RESULT=$?
	if [ $RESULT != 0 ]; then
		echo "Failed to lint $file, aborting"
		exit $RESULT
	fi
done
