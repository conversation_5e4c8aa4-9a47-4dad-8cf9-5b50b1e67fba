p.PHONY: install_deps clean format check test build buildonly git_version setup-ci setup-dev
SHELL := /bin/bash
.SHELLFLAGS := -euo pipefail -c


MAKEFILE_DIR := $(dir $(abspath $(lastword $(MAKEFILE_LIST))))
VENV_BIN := $(MAKEFILE_DIR).venv/bin
GHA_FOLDER := $(MAKEFILE_DIR)../../.github
PYTHON_VERSION := $(shell cat .python-version | sed 's/^\(3\.[0-9]*\)\..*/\1/')
VERSION := $(shell uv run --no-project -p 3.12 - <<< "import tomllib; print(tomllib.load(open('pyproject.toml', 'rb'))['project']['version'])")
# Expected artifact paths
WHEEL := dist/spark_validator-$(VERSION)-py3-none-any.whl
EGG   := dist/spark_validator-$(VERSION)-py$(PYTHON_VERSION).egg


$(VENV_BIN)/python:  ## Create .venv using uv
	@echo "*** Creating virtual environment using uv in ./.venv... ***"
	@uv venv --seed --python $$(<.python-version)
	@echo "*** Activate the virtual environment with: source .venv/bin/activate ***"

venv: $(VENV_BIN)/python

setup-dev: $(VENV_BIN)/python  ## Install development dependencies
	@uv pip install --python $(VENV_BIN)/python -e .[dev]

install_deps: setup-dev ## Alias for setup-dev

setup-ci: $(VENV_BIN)/python  ## Install CI dependencies
	@uv pip install --python $(VENV_BIN)/python '.[ci,dev]'

clean:
	@echo "*** Cleaning the repo (excluding .venv)... ***"
	@rm -rf `find . -name __pycache__`
	@rm -rf .pytest_cache
	@rm -rf spark-warehouse
	@rm -rf build
	@rm -rf dist
	@rm -rf unarchiver.egg-info

clean-venv:  ## Delete virtual environment
	rm -rf .venv

format: ## Format code using black
	@$(VENV_BIN)/black --line-length 120 unarchiver tests

check: $(VENV_BIN)/flake8 $(VENV_BIN)/mypy ## Run code checks
	@echo "*** Running flake8... ***"
	@source $(VENV_BIN)/activate; $(VENV_BIN)/flake8 unarchiver tests
	@echo "*** Running mypy... ***"
	@source $(VENV_BIN)/activate; $(VENV_BIN)/mypy --ignore-missing-imports unarchiver tests

test: check ## Run full test suite
	@$(VENV_BIN)/pytest tests/

build: clean install_deps test buildinfo ## Run tests and build distributions

$(WHEEL):
	uv build .

$(EGG): $(VENV_BIN)/python
	$(VENV_BIN)/python setup.py bdist_egg

buildonly: $(WHEEL) $(EGG)
	@echo "*** Build complete. Artifacts: ***"


dist/GIT_VERSION:
	@echo "*** Creating GIT_VERSION file... ***"
	@if [ -n "$(VERSION)" ]; then \
	   uv run --no-project -p 3.12 $(GHA_FOLDER)/scripts/gen-git_version.py --version $(VERSION) > dist/GIT_VERSION; \
	else \
	   uv run --no-project -p 3.12 $(GHA_FOLDER)/scripts/gen-git_version.py > dist/GIT_VERSION; \
	fi;

git_version: $(WHEEL) $(EGG)  dist/GIT_VERSION ## Inject GIT_VERSION file into dist
	@cd dist && ls *.{whl,egg} | xargs -I % zip -r % GIT_VERSION
	@echo "*** Added GIT_VERSION file to the wheel and egg files with value: $$(<dist/GIT_VERSION) ***"

git-version: git_version

get-version:  ## Print current version from pyproject.toml
	@echo "${VERSION}"

set-version:  ## Set version in pyproject.toml (requires VERSION=)
	@test -n "$(VERSION)" || (echo "Error: VERSION missing. Use 'make VERSION=<new_version> set-version'" && exit 1)
	@uv run --no-project -p 3.12 $(GHA_FOLDER)/scripts/bump-version-pyproject.py --version "$(VERSION)"

bump-version:  ## Bump patch version
	@uv run --no-project -p 3.12 $(GHA_FOLDER)/scripts/bump-version-pyproject.py

bump-prerelease:  ## Bump pre-release version
	@uv run --no-project -p 3.12 $(GHA_FOLDER)/scripts/bump-version-pyproject.py --pre-release

# Distribution
publish: git_version  ## Publish to PyPI
	@ALREADY_DEPLOYED=$$(curl -s --netrc -X GET 'https://nexus.eng.aetion.com/service/rest/v1/search?name=unarchiver' | jq --arg ver "${VERSION}" '[.items[] | select(.version == $$ver)] | length'); \
	if [ "$${ALREADY_DEPLOYED}" -eq "0" ]; then \
	    echo "*** Publishing v${VERSION} to PyPI... ***"; \
        uv run --no-project -p 3.12 --with twine==6.0.1 twine upload -r aetion-nexus dist/*.{whl,egg,gz}; \
	else \
		echo "Version ${VERSION} already exists in PyPI. Skipping upload."; \
	fi;

deploy:  ## Deploy to environment (ENV= required)
	@./deploy.sh $(ENV)